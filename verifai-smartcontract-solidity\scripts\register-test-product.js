const hre = require("hardhat");

async function main() {
    console.log("🔍 Registering test product on blockchain...\n");

    // Get the contract factory
    const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
    
    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully");
        
        // Product details (matching what was registered in the frontend)
        const productData = {
            name: "Sample Product", // This should match your actual product name
            brand: "Sample Brand", // This should match your actual brand
            serialNumber: "dffg", // This matches the QR code you were scanning
            description: "Test product for blockchain verification",
            image: "test-image.jpg",
            actor: "Test Manufacturer",
            location: "Test Location",
            timestamp: Math.floor(Date.now() / 1000).toString() // Current timestamp
        };
        
        console.log("📦 Registering product with data:", productData);
        
        // Register the product
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction submitted:", registerTx.hash);
        console.log("⏳ Waiting for confirmation...");
        
        // Wait for transaction confirmation
        const receipt = await registerTx.wait();
        console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
        
        // Verify the product was registered using checkProduct first
        console.log("\n🔍 Verifying product registration with checkProduct...");
        const checkResult = await contract.checkProduct(productData.serialNumber);
        console.log("Check result:", checkResult);

        if (checkResult[0]) { // exists = true
            console.log("✅ Product exists! Now trying getProduct...");
            const product = await contract.getProduct(productData.serialNumber);
        
        if (product && product[0] && product[0] !== "") {
            console.log("✅ Product successfully registered!");
            console.log("   Serial Number:", product[0]);
            console.log("   Name:", product[1]);
            console.log("   Brand:", product[2]);
            console.log("   Description:", product[3]);
            console.log("   Image:", product[4]);
            console.log("   History Length:", product[5]?.length || 0);
            
            if (product[5] && product[5].length > 0) {
                console.log("📋 Product History:");
                product[5].forEach((entry, index) => {
                    console.log(`   ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Timestamp: ${entry.timestamp}, Sold: ${entry.isSold}`);
                });
            }
            } else {
                console.log("❌ Product registration failed - product not found");
            }
        } else {
            console.log("❌ Product does not exist according to checkProduct");
        }
        
        // Add a supply chain update for testing
        console.log("\n📦 Adding supply chain update...");
        const updateTx = await contract.addProductHistory(
            productData.serialNumber,
            "Test Supplier",
            "Supplier Location",
            (Math.floor(Date.now() / 1000) + 3600).toString(), // 1 hour later
            false // not sold
        );
        
        console.log("⏳ Update transaction submitted:", updateTx.hash);
        await updateTx.wait();
        console.log("✅ Supply chain update confirmed");
        
        // Verify the updated product
        console.log("\n🔍 Verifying updated product...");
        const updatedProduct = await contract.getProduct(productData.serialNumber);
        
        if (updatedProduct && updatedProduct[5] && updatedProduct[5].length > 0) {
            console.log("✅ Product history updated!");
            console.log("   History Length:", updatedProduct[5].length);
            console.log("📋 Complete Product History:");
            updatedProduct[5].forEach((entry, index) => {
                console.log(`   ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Timestamp: ${entry.timestamp}, Sold: ${entry.isSold}`);
            });
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
    
    console.log("\n🏁 Product registration complete!");
    console.log("🎯 You can now scan the QR code to see the product history!");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Registration failed:", error);
        process.exit(1);
    });
