const hre = require("hardhat");

async function main() {
    console.log("🔍 Checking specific product in blockchain...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");

        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);

        console.log("✅ Connected to contract successfully\n");

        // Check the specific product from your QR code or command line argument
        const serialNumber = process.argv[2] || "TEST123";
        
        console.log(`🔍 Checking product with serial number: "${serialNumber}"`);
        
        try {
            const product = await contract.getProduct(serialNumber);
            
            console.log("✅ Product found!");
            console.log("📦 Product Details:");
            console.log(`   Serial Number: ${product[0]}`);
            console.log(`   Name: ${product[1]}`);
            console.log(`   Brand: ${product[2]}`);
            console.log(`   Description: ${product[3]}`);
            console.log(`   Image: ${product[4]}`);
            if (product[5] && product[5].length > 0) {
                console.log(`   History: ${product[5].length} entries`);
                product[5].forEach((entry, index) => {
                    console.log(`      ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Timestamp: ${entry.timestamp}`);
                });
            }
            console.log(`   🔗 QR Code: ${contractAddress},${serialNumber}`);
            
            console.log("\n🎉 This product is AUTHENTIC!");
            console.log("✅ Your QR scanner should route to authentic-product page");
            
        } catch (error) {
            console.log(`❌ Product not found for serial number: "${serialNumber}"`);
            console.log("Error:", error.message);
            
            console.log("\n💡 This means:");
            console.log("1. The product hasn't been registered yet");
            console.log("2. The serial number might be incorrect");
            console.log("3. The product might be counterfeit");
            
            console.log("\n🔧 To fix this:");
            console.log("1. Register a product with serial number 'dffg'");
            console.log("2. Or use a different QR code with an existing product");
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
}

// Execute the check
if (require.main === module) {
    main()
        .then(() => {
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
