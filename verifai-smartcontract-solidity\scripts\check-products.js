const hre = require("hardhat");

async function main() {
    console.log("🔍 Checking products in blockchain...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully\n");
        
        // List of potential serial numbers to check
        const serialNumbers = [
            "popoos",
            "TEST123",
            "WTEST123",
            "test123",
            "dffg",
            "product1",
            "sample",
            "demo"
        ];
        
        console.log("🔍 Checking for products with these serial numbers:");
        console.log(serialNumbers.join(", "));
        console.log("");
        
        let foundProducts = 0;
        
        for (const serialNumber of serialNumbers) {
            try {
                console.log(`🔍 Checking serial number: "${serialNumber}"`);
                
                const product = await contract.getProduct(serialNumber);
                
                if (product && product[0] && product[0] !== "") {
                    foundProducts++;
                    console.log(`✅ FOUND PRODUCT: "${serialNumber}"`);
                    console.log(`   Serial Number: ${product[0]}`);
                    console.log(`   Name: ${product[1]}`);
                    console.log(`   Brand: ${product[2]}`);
                    console.log(`   Description: ${product[3]}`);
                    console.log(`   Image: ${product[4]}`);
                    console.log(`   History Length: ${product[5]?.length || 0}`);
                    
                    if (product[5] && product[5].length > 0) {
                        console.log(`   📋 History:`);
                        product[5].forEach((entry, index) => {
                            console.log(`      ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Timestamp: ${entry.timestamp}, Sold: ${entry.isSold}`);
                        });
                    }
                    
                    console.log(`   🔗 QR Code: ${contractAddress},${serialNumber}`);
                    console.log("");
                } else {
                    console.log(`❌ No product found for: "${serialNumber}"`);
                }
            } catch (error) {
                console.log(`❌ Error checking "${serialNumber}": ${error.message}`);
            }
        }
        
        console.log(`\n📊 Summary: Found ${foundProducts} products out of ${serialNumbers.length} checked`);
        
        if (foundProducts === 0) {
            console.log("\n⚠️  No products found! You may need to register some test products first.");
            console.log("   Run: node scripts/register-test-product.js");
        } else {
            console.log("\n🎯 You can test these products in the frontend!");
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Check failed:", error);
        process.exit(1);
    });
