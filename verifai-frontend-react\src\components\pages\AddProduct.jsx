import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Stack,
  TextField,
  Snackbar,
  Alert,
  Typography,
  Paper,
  Card,
  CardMedia,
  Divider,
  Container,
  CardContent,
  Chip,
  LinearProgress,
  Fade,
  Zoom,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Avatar
} from '@mui/material';
import {
  Add,
  CloudUpload,
  QrCode,
  Fingerprint,
  Inventory,
  Store,
  Description,
  Image,
  Security,
  CheckCircle,
  Cancel,
  Download,
  ArrowBack,
  AutoAwesome,
  AccountBalanceWallet,
  Verified,
  LocationOn,
  Person,
  Schedule
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import { BrowserProvider, Contract } from 'ethers';
import { useNavigate } from 'react-router-dom';
import { apiPost, apiGet } from '../../utils/apiUtils';
import QRCode from 'react-qr-code';
import dayjs from 'dayjs';

import bgImg from '../../img/bg.png';
import abi from '../../utils/VerifaiDebug.json';
import useAuth from '../../hooks/useAuth';
import MetaMaskChecker from '../MetaMaskChecker';

const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "******************************************";
const contractABI = abi.abi;

// Advanced Animations
const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(46, 125, 50, 0.3),
                0 0 40px rgba(46, 125, 50, 0.1),
                inset 0 0 20px rgba(46, 125, 50, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(46, 125, 50, 0.6),
                0 0 80px rgba(46, 125, 50, 0.2),
                inset 0 0 40px rgba(46, 125, 50, 0.2);
  }
`;

const float = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-5px) rotate(1deg); }
  50% { transform: translateY(-10px) rotate(0deg); }
  75% { transform: translateY(-5px) rotate(-1deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
    transform: skewX(-20deg);
  }
  100% {
    background-position: 200% 0;
    transform: skewX(-20deg);
  }
`;

const hologram = keyframes`
  0%, 100% {
    opacity: 0.8;
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    opacity: 0.9;
    filter: hue-rotate(90deg) brightness(1.1);
  }
  50% {
    opacity: 1;
    filter: hue-rotate(180deg) brightness(1.2);
  }
  75% {
    opacity: 0.9;
    filter: hue-rotate(270deg) brightness(1.1);
  }
`;

const dataStream = keyframes`
  0% { transform: translateY(100vh) scaleY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) scaleY(1); opacity: 0; }
`;

const scanLine = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
`;

const matrixRain = keyframes`
  0% { transform: translateY(-100vh); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
`;

const energyPulse = keyframes`
  0% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(46, 125, 50, 0);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
    transform: scale(1);
  }
`;

// Advanced Styled Components
const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `
    radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.15)} 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, ${alpha(theme.palette.info.main, 0.1)} 0%, transparent 50%),
    linear-gradient(135deg,
      ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
      ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 30%,
      ${theme.palette.mode === 'dark' ? '#16213e' : '#cbd5e1'} 70%,
      ${theme.palette.mode === 'dark' ? '#0f172a' : '#94a3b8'} 100%
    )
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.primary.main, 0.03)} 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.primary.main, 0.03)} 100px
      )
    `,
    pointerEvents: 'none',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 10% 20%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 30%),
      radial-gradient(circle at 90% 80%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 30%),
      radial-gradient(circle at 50% 50%, ${alpha(theme.palette.success.main, 0.05)} 0%, transparent 40%)
    `,
    animation: `${hologram} 8s ease-in-out infinite`,
    pointerEvents: 'none',
  },
}));

// Particle Effect Component
const ParticleField = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  pointerEvents: 'none',
  '&::before, &::after': {
    content: '""',
    position: 'absolute',
    width: '2px',
    height: '2px',
    background: theme.palette.primary.main,
    borderRadius: '50%',
    animation: `${dataStream} 3s linear infinite`,
  },
  '&::before': {
    left: '20%',
    animationDelay: '0s',
    boxShadow: `
      0 0 6px ${theme.palette.primary.main},
      0 0 12px ${theme.palette.primary.main},
      0 0 18px ${theme.palette.primary.main}
    `,
  },
  '&::after': {
    left: '80%',
    animationDelay: '1.5s',
    background: theme.palette.secondary.main,
    boxShadow: `
      0 0 6px ${theme.palette.secondary.main},
      0 0 12px ${theme.palette.secondary.main},
      0 0 18px ${theme.palette.secondary.main}
    `,
  },
}));

// Scanning Line Effect
const ScanLine = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: 0,
  width: '100%',
  height: '2px',
  background: `linear-gradient(90deg,
    transparent 0%,
    ${theme.palette.primary.main} 50%,
    transparent 100%
  )`,
  animation: `${scanLine} 4s ease-in-out infinite`,
  boxShadow: `
    0 0 10px ${theme.palette.primary.main},
    0 0 20px ${theme.palette.primary.main},
    0 0 30px ${theme.palette.primary.main}
  `,
  pointerEvents: 'none',
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: `
    linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.paper, 0.85)} 50%,
      ${alpha(theme.palette.background.paper, 0.75)} 100%
    )
  `,
  backdropFilter: 'blur(25px) saturate(180%)',
  border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
  borderRadius: '28px',
  boxShadow: `
    0 25px 50px ${alpha(theme.palette.common.black, 0.15)},
    0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: `linear-gradient(90deg,
      ${theme.palette.primary.main} 0%,
      ${theme.palette.secondary.main} 50%,
      ${theme.palette.info.main} 100%
    )`,
    borderRadius: '28px 28px 0 0',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg,
      transparent,
      ${alpha(theme.palette.primary.main, 0.1)},
      transparent
    )`,
    transition: 'left 0.6s ease',
    borderRadius: '28px',
  },
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    boxShadow: `
      0 40px 80px ${alpha(theme.palette.common.black, 0.2)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)},
      0 0 40px ${alpha(theme.palette.primary.main, 0.1)},
      inset 0 1px 0 ${alpha(theme.palette.common.white, 0.3)}
    `,
    '&::after': {
      left: '100%',
    },
  },
}));

// Enhanced Info Card with Holographic Effects
const HolographicCard = styled(Card)(({ theme }) => ({
  background: `
    linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.9)} 0%,
      ${alpha(theme.palette.background.paper, 0.7)} 100%
    )
  `,
  backdropFilter: 'blur(20px) saturate(150%)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  borderRadius: '20px',
  padding: '20px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 70%)
    `,
    animation: `${pulse} 3s ease-in-out infinite`,
    pointerEvents: 'none',
  },
  '&:hover': {
    transform: 'translateY(-6px) scale(1.02)',
    boxShadow: `
      0 15px 35px ${alpha(theme.palette.common.black, 0.15)},
      0 0 20px ${alpha(theme.palette.primary.main, 0.1)}
    `,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.4)}`,
    '&::before': {
      background: `
        radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 70%)
      `,
    },
  },
}));

const InfoCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.8)} 0%,
    ${alpha(theme.palette.background.paper, 0.6)} 100%
  )`,
  backdropFilter: 'blur(15px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '20px',
  padding: '24px',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const NeonTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.8)} 0%,
      ${alpha(theme.palette.background.paper, 0.6)} 100%
    )`,
    backdropFilter: 'blur(10px)',
    borderRadius: '16px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '& fieldset': {
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
      borderRadius: '16px',
    },
    '&:hover fieldset': {
      border: `1px solid ${alpha(theme.palette.primary.main, 0.4)}`,
      boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
    },
    '&.Mui-focused fieldset': {
      border: `2px solid ${theme.palette.primary.main}`,
      boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.2)}`,
    },
  },
  '& .MuiInputLabel-root': {
    fontWeight: 600,
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
  },
}));

const CyberButton = styled(Button)(({ theme, variant: buttonVariant }) => ({
  borderRadius: '12px',
  padding: '12px 32px',
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  background: buttonVariant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: buttonVariant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: buttonVariant === 'primary' ? 'none' : `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  boxShadow: buttonVariant === 'primary'
    ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 12px ${alpha(theme.palette.common.black, 0.1)}`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: buttonVariant === 'primary'
      ? `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 20px ${alpha(theme.palette.common.black, 0.15)}`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));

const UploadZone = styled(Box)(({ theme, isDragActive }) => ({
  border: `2px dashed ${isDragActive ? theme.palette.primary.main : alpha(theme.palette.primary.main, 0.3)}`,
  borderRadius: '20px',
  padding: '40px 20px',
  textAlign: 'center',
  background: isDragActive
    ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  backdropFilter: 'blur(10px)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  '&:hover': {
    border: `2px dashed ${theme.palette.primary.main}`,
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 30px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const AddProduct = () => {
  const [formData, setFormData] = useState({
    serialNumber: '',
    name: '',
    brand: '',
    description: ''
  });
  const [image, setImage] = useState({ file: null, filepreview: null });
  const [qrData, setQrData] = useState('');
  const [manufacturerData, setManufacturerData] = useState({
    name: '',
    date: '',
    latitude: '',
    longitude: '',
    location: ''
  });
  const [loading, setLoading] = useState(false);
  const [transactionStatus, setTransactionStatus] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [registrationProgress, setRegistrationProgress] = useState({
    step: 0,
    total: 4,
    currentStep: '',
    isActive: false
  });
  const [registrationComplete, setRegistrationComplete] = useState(false);

  const { auth, user, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const initialize = async () => {
      try {
        // Wait for authentication to be loaded
        if (isLoading) {
          console.log('Authentication still loading, waiting...');
          return;
        }

        if (!isAuthenticated) {
          console.log('User not authenticated, redirecting to login');
          navigate('/login');
          return;
        }

        const account = await findMetaMaskAccount();
        if (account) {
          await getUsername();
          getCurrentTimeLocation();
        }
      } catch (error) {
        console.error('Initialization error:', error);
      }
    };
    initialize();
  }, [isLoading, isAuthenticated, navigate]);

  const resetForm = () => {
    setFormData({
      serialNumber: '',
      name: '',
      brand: '',
      description: ''
    });
    setImage({ file: null, filepreview: null });
    setQrData('');
    setRegistrationComplete(false);
  };

  const findMetaMaskAccount = async () => {
    try {
      // Check if MetaMask is available
      if (typeof window.ethereum === 'undefined') {
        setTransactionStatus({
          open: true,
          message: 'MetaMask is not installed. Please install MetaMask extension to continue.',
          severity: 'warning'
        });
        return null;
      }

      const provider = getEthereumObject();

      // Try to get accounts
      const accounts = await provider.send('eth_accounts', []);

      if (accounts.length === 0) {
        // No accounts connected, try to request connection
        try {
          const requestedAccounts = await provider.send('eth_requestAccounts', []);
          return requestedAccounts.length ? requestedAccounts[0] : null;
        } catch (requestError) {
          console.warn('User rejected MetaMask connection:', requestError);
          setTransactionStatus({
            open: true,
            message: 'Please connect your MetaMask wallet to continue.',
            severity: 'info'
          });
          return null;
        }
      }

      return accounts[0];
    } catch (error) {
      console.error('findMetaMaskAccount error:', error);

      if (error.message.includes('MetaMask is not installed')) {
        setTransactionStatus({
          open: true,
          message: 'MetaMask extension not found. Please install MetaMask from https://metamask.io/',
          severity: 'error'
        });
      } else {
        setTransactionStatus({
          open: true,
          message: 'Error connecting to MetaMask. Please refresh and try again.',
          severity: 'error'
        });
      }

      return null;
    }
  };

  const getEthereumObject = () => {
    // Check if MetaMask is installed
    if (typeof window.ethereum === 'undefined') {
      throw new Error('MetaMask is not installed. Please install MetaMask extension from https://metamask.io/');
    }

    // Check if MetaMask is the provider
    if (!window.ethereum.isMetaMask) {
      console.warn('MetaMask is not the active provider');
    }

    console.log('MetaMask detected:', {
      isMetaMask: window.ethereum.isMetaMask,
      chainId: window.ethereum.chainId,
      selectedAddress: window.ethereum.selectedAddress
    });

    return new BrowserProvider(window.ethereum);
  };

  const getUsername = async () => {
    try {
      // Use the username from the user object
      const username = auth?.user?.username || user?.username;

      if (!username) {
        console.warn('No username available for profile lookup');
        return;
      }

      const res = await apiGet(`/profile/${username}`);
      setManufacturerData(prev => ({
        ...prev,
        name: res?.data[0]?.name || ''
      }));
    } catch (error) {
      console.error('getUsername error:', error);

      // Handle specific error cases
      if (error.response?.status === 403) {
        console.error('Access forbidden - check authentication and permissions');
      } else if (error.response?.status === 401) {
        console.error('Unauthorized - token may be expired or invalid');
      }
    }
  };

  const getCurrentTimeLocation = () => {
    setManufacturerData(prev => ({
      ...prev,
      date: dayjs().unix()
    }));

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        const locationString = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;

        setManufacturerData(prev => ({
          ...prev,
          latitude: lat,
          longitude: lng,
          location: locationString
        }));
      },
      (error) => {
        console.error('Geolocation error:', error);
        // Set default location if geolocation fails
        setManufacturerData(prev => ({
          ...prev,
          location: 'Location unavailable'
        }));
      }
    );
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

const ALLOWED_TYPES = ['image/jpeg', 'image/png'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

const handleImageUpload = (e) => {
  const file = e.target.files?.[0];
  if (!file) return;

  if (!ALLOWED_TYPES.includes(file.type)) {
    alert('Only JPEG and PNG files are allowed.');
    return;
  }

  if (file.size > MAX_FILE_SIZE) {
    alert('File is too large. Maximum size is 5MB.');
    return;
  }

  setImage({
    file,
    filepreview: URL.createObjectURL(file),
  });
};


  const generateQRCode = (serialNumber) => {
    setQrData(`${CONTRACT_ADDRESS},${serialNumber}`);
  };

  const downloadQR = () => {
    try {
      const svg = document.getElementById('QRCode');
      if (!svg) {
        console.error('QR Code element not found');
        return;
      }

      const svgData = new XMLSerializer().serializeToString(svg);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Use window.Image instead of Image constructor
      const img = new window.Image();
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = `${formData.serialNumber || 'qrcode'}.png`;
        downloadLink.href = pngFile;
        downloadLink.click();
      };

      img.onerror = () => {
        console.error('Failed to load QR code image');
      };

      img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    } catch (error) {
      console.error('Error downloading QR code:', error);
    }
  };

  const uploadImage = async () => {
    const data = new FormData();
    data.append('image', image.file);
    try {
      const res = await apiPost('/upload/product', data, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return res.data.success === 1;
    } catch (error) {
      console.error('uploadImage error:', error);
      if (error.response?.status === 429) {
        console.warn("Rate limit exceeded during image upload. Please try again.");
      }
      throw error;
    }
  };

  const registerProduct = async () => {
    setLoading(true);
    try {
      console.log('🔍 Starting product registration...');

      // Update status for user feedback
      setTransactionStatus({
        open: true,
        message: 'Initializing blockchain connection...',
        severity: 'info'
      });

      if (!window.ethereum) {
        throw new Error('MetaMask is not installed. Please install MetaMask to continue.');
      }

      const provider = new BrowserProvider(window.ethereum);
      console.log('✅ Provider created');

      // Update status
      setTransactionStatus({
        open: true,
        message: 'Connecting to your wallet...',
        severity: 'info'
      });

      const signer = await provider.getSigner();
      console.log('✅ Signer obtained:', await signer.getAddress());

      if (!CONTRACT_ADDRESS) {
        throw new Error('Contract address is not configured. Please check your environment variables.');
      }

      if (!contractABI) {
        throw new Error('Contract ABI is not available. Please check the ABI file.');
      }

      const contract = new Contract(CONTRACT_ADDRESS, contractABI, signer);
      console.log('✅ Contract instance created');
      console.log('Contract object:', contract);

      // Verify contract has the required function
      if (!contract.registerProduct) {
        throw new Error('Contract does not have registerProduct function. Please check the ABI.');
      }

      console.log('✅ Contract functions verified');
      console.log('Contract created successfully');

      const formattedDesc = formData.description.replace(/,/g, ';');

      console.log('🔍 Checking all required data...');
      console.log('formData:', formData);
      console.log('image:', image);
      console.log('image.file:', image?.file);
      console.log('manufacturerData:', manufacturerData);

      // Check for undefined values that could cause the error
      if (!formData) {
        throw new Error('Form data is undefined');
      }
      if (!image || !image.file) {
        throw new Error('Image file is undefined. Please upload an image.');
      }
      if (!manufacturerData) {
        throw new Error('Manufacturer data is undefined');
      }

      // Ensure all required manufacturerData fields have values
      const safeManufacturerData = {
        name: manufacturerData.name || 'Unknown Manufacturer',
        location: manufacturerData.location || `${manufacturerData.latitude || 0}, ${manufacturerData.longitude || 0}`,
        date: manufacturerData.date || dayjs().unix()
      };

      console.log('Safe manufacturer data:', safeManufacturerData);

      console.log('Registering product with parameters:', {
        name: formData.name,
        brand: formData.brand,
        serialNumber: formData.serialNumber,
        description: formattedDesc,
        image: image.file.name,
        actor: safeManufacturerData.name,
        location: safeManufacturerData.location,
        timestamp: safeManufacturerData.date.toString()
      });

      // Note: The smart contract now handles duplicate checking automatically
      console.log('Proceeding with product registration...');

      // Enhanced gas estimation with multiple fallback strategies
      let gasLimit;
      let gasPrice;

      try {
        // Update status
        setTransactionStatus({
          open: true,
          message: 'Preparing transaction parameters...',
          severity: 'info'
        });

        console.log('🔍 Optimizing gas estimation...');

        // For Ganache/local networks, use legacy gas pricing to avoid EIP-1559 errors
        const network = await provider.getNetwork();
        console.log('Network info:', {
          chainId: network.chainId.toString(),
          name: network.name
        });

        // Check if this is a local network (Ganache/Hardhat)
        const isLocalNetwork = network.chainId === 1337n || network.chainId === 31337n;

        if (isLocalNetwork) {
          console.log('🏠 Local network detected, using optimized settings');

          // For local networks, use conservative but fast settings
          // Skip gas estimation to speed up the process
          gasLimit = 800000; // Conservative but sufficient for most operations
          gasPrice = BigInt('20000000000'); // 20 gwei for local network

          console.log('⚡ Using optimized local network settings');

        } else {
          // For external networks, do a quick gas estimation with timeout
          try {
            setTransactionStatus({
              open: true,
              message: 'Estimating gas requirements...',
              severity: 'info'
            });

            // Add timeout to gas estimation to prevent hanging
            const gasEstimatePromise = contract.registerProduct.estimateGas(
              formData.name,
              formData.brand,
              formData.serialNumber,
              formattedDesc,
              image.file.name,
              safeManufacturerData.name,
              safeManufacturerData.location,
              safeManufacturerData.date.toString()
            );

            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Gas estimation timeout')), 10000) // 10 second timeout
            );

            const gasEstimate = await Promise.race([gasEstimatePromise, timeoutPromise]);
            console.log('✅ Estimated gas:', gasEstimate.toString());

            gasLimit = Math.floor(Number(gasEstimate) * 1.3); // Reduced buffer for speed

            // Quick fee data fetch with fallback
            try {
              const feeData = await provider.getFeeData();
              gasPrice = feeData.gasPrice || BigInt('20000000000');
            } catch {
              gasPrice = BigInt('20000000000'); // Fallback
            }

          } catch (feeError) {
            console.warn('Gas estimation timed out or failed, using safe defaults');
            gasLimit = 800000; // Conservative fallback
            gasPrice = BigInt('20000000000');
          }
        }

        console.log('Using gas limit:', gasLimit);
        console.log('Using gas price:', gasPrice.toString());

      } catch (gasError) {
        console.error('❌ Gas setup failed:', gasError);

        // Use fast fallback values
        gasLimit = 800000; // Conservative but fast
        gasPrice = BigInt('20000000000'); // 20 gwei

        console.log('⚡ Using fast fallback settings');
      }

      // Prepare transaction options with legacy gas pricing for local networks
      const txOptions = {
        gasLimit: gasLimit,
        gasPrice: gasPrice, // Use legacy gasPrice instead of EIP-1559
        // Remove EIP-1559 fields to avoid MetaMask errors
        type: 0 // Explicitly use legacy transaction type
      };

      // Update status before transaction
      setTransactionStatus({
        open: true,
        message: 'Submitting transaction to blockchain...',
        severity: 'info'
      });

      console.log('🚀 Submitting transaction with options:', txOptions);

      const tx = await contract.registerProduct(
        formData.name,
        formData.brand,
        formData.serialNumber,
        formattedDesc,
        image.file.name,
        safeManufacturerData.name,
        safeManufacturerData.location,
        safeManufacturerData.date.toString(),
        txOptions
      );

      console.log('Transaction submitted:', tx.hash);

      setTransactionStatus({
        open: true,
        message: `Transaction submitted! Waiting for confirmation...`,
        severity: 'info'
      });

      // Add timeout to transaction waiting to prevent infinite hanging
      const receiptPromise = tx.wait();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Transaction confirmation timeout')), 60000) // 60 second timeout
      );

      const receipt = await Promise.race([receiptPromise, timeoutPromise]);
      console.log('Transaction confirmed:', receipt);
      console.log('Transaction status:', receipt.status);
      console.log('Gas used:', receipt.gasUsed?.toString());

      // Check if transaction was successful
      if (receipt.status !== 1) {
        throw new Error('Transaction failed - check transaction receipt for details');
      }

      // Update status for successful transaction
      setTransactionStatus({
        open: true,
        message: 'Transaction confirmed! Finalizing registration...',
        severity: 'success'
      });

      // Skip verification to speed up the process
      console.log('✅ Transaction successful! Registration complete.');
      console.log('📝 Product can be verified by scanning the QR code.');

      // No verification delay - proceed immediately to completion

      generateQRCode(formData.serialNumber);
      setRegistrationComplete(true);

      setTransactionStatus({
        open: true,
        message: 'Product successfully registered and verified on blockchain!',
        severity: 'success'
      });
    } catch (error) {
      console.error('registerProduct error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addProductDB = async () => {
    try {
      const body = JSON.stringify({ 
        serialNumber: formData.serialNumber, 
        name: formData.name, 
        brand: formData.brand 
      });
      await apiPost('/addproduct', body, {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('addProductDB error:', error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (registrationComplete) {
      resetForm();
      return;
    }

    if (!formData.serialNumber || !formData.name || !formData.brand || !formData.description || !image.file) {
      return setTransactionStatus({
        open: true,
        message: 'Please fill all required fields',
        severity: 'warning',
      });
    }

    try {
      // Optimized flow: Run blockchain registration first (most critical)
      // Then run image upload and DB operations in parallel

      setTransactionStatus({
        open: true,
        message: 'Starting registration process...',
        severity: 'info'
      });

      // Step 1: Blockchain registration (most important and time-consuming)
      await registerProduct();

      // Step 2: Run image upload and database operations in parallel for speed
      setTransactionStatus({
        open: true,
        message: 'Finalizing registration details...',
        severity: 'info'
      });

      await Promise.all([
        uploadImage().catch(err => {
          console.warn('Image upload failed (non-critical):', err);
          return false; // Don't fail the entire process
        }),
        addProductDB().catch(err => {
          console.warn('Database update failed (non-critical):', err);
          return false; // Don't fail the entire process
        })
      ]);

      // Success - registration is complete
      setTransactionStatus({
        open: true,
        message: 'Product successfully registered on blockchain!',
        severity: 'success'
      });

    } catch (error) {
      console.error('Registration error:', error);

      let errorMessage = 'An unexpected error occurred';

      if (error.message.includes('user rejected transaction') || error.message.includes('user rejected')) {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message.includes('Product with this serial number already exists')) {
        errorMessage = `Product with serial number "${formData.serialNumber}" already exists on the blockchain. Please use a different serial number.`;
      } else if (error.message.includes('Transaction simulation failed')) {
        errorMessage = 'Transaction simulation failed. Please check your inputs and try again.';
      } else if (error.message.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds to complete the transaction. Please check your wallet balance.';
      } else if (error.message.includes('gas')) {
        errorMessage = 'Transaction failed due to gas issues. Please try again with higher gas limit.';
      } else {
        errorMessage = `Error: ${error.message}`;
      }

      setTransactionStatus({
        open: true,
        message: errorMessage,
        severity: 'error',
      });
    }
  };

  const handleCloseSnackbar = () => {
    setTransactionStatus(prev => ({ ...prev, open: false }));
  };

  const theme = useTheme();

  return (
    <FuturisticContainer>
      {/* Advanced Background Effects */}
      <ParticleField />
      <ScanLine />

      {/* Matrix Rain Effect */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          '&::before, &::after': {
            content: '"01010101"',
            position: 'absolute',
            color: alpha(theme.palette.primary.main, 0.1),
            fontSize: '12px',
            fontFamily: 'monospace',
            animation: `${matrixRain} 8s linear infinite`,
            whiteSpace: 'nowrap',
          },
          '&::before': {
            left: '10%',
            animationDelay: '0s',
          },
          '&::after': {
            left: '90%',
            animationDelay: '4s',
          },
        }}
      />

      <Container maxWidth="lg" sx={{ py: 4, position: 'relative', zIndex: 10 }}>
        <Fade in timeout={1000}>
          <Box>
            {/* Header Section */}
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                <IconButton
                  onClick={() => navigate(-1)}
                  sx={{
                    mr: 3,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
                    backdropFilter: 'blur(10px)',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    '&:hover': {
                      background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                      transform: 'translateY(-2px)',
                    }
                  }}
                >
                  <ArrowBack />
                </IconButton>
                <AutoAwesome sx={{ fontSize: 48, color: theme.palette.primary.main, mr: 2 }} />
                <Typography
                  variant="h2"
                  sx={{
                    background: `linear-gradient(135deg,
                      ${theme.palette.primary.main} 0%,
                      ${theme.palette.secondary.main} 50%,
                      ${theme.palette.primary.dark} 100%
                    )`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontWeight: 700,
                    fontSize: { xs: '2.5rem', md: '3.5rem' },
                    letterSpacing: '-0.02em',
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                  }}
                >
                  {registrationComplete ? 'Registration Complete' : 'Register New Product'}
                </Typography>
              </Box>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                  color: theme.palette.mode === 'dark' ? '#e2e8f0' : '#475569',
                  fontWeight: 500,
                  fontSize: '1.1rem',
                  letterSpacing: '0.05em',
                  mb: 2,
                  position: 'relative',
                  '&::before': {
                    content: '">"',
                    color: theme.palette.primary.main,
                    marginRight: '8px',
                    fontWeight: 700,
                  },
                }}
              >
                {registrationComplete
                  ? 'Blockchain Registration Protocol Complete'
                  : 'Blockchain Product Registration System'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, opacity: 0.8 }}>
                {registrationComplete
                  ? 'Your product has been successfully registered on the blockchain'
                  : 'Fill in the product details below to register it on the blockchain'}
              </Typography>
            </Box>

            {/* MetaMask Connection Status */}
            <GlassCard sx={{ mb: 4 }}>
              <CardContent sx={{ p: 3 }}>
                <MetaMaskChecker onMetaMaskReady={(status) => {
                  console.log('MetaMask ready:', status);
                }} />
              </CardContent>
            </GlassCard>

            <form onSubmit={handleSubmit}>
              {!registrationComplete ? (
                <Grid container spacing={4} sx={{ mb: 4 }}>
                  {/* Product Information Section */}
                  <Grid item xs={12} lg={8}>
                    <GlassCard sx={{ height: '100%' }}>
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Inventory sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 32 }} />
                          <Typography variant="h4" sx={{ fontWeight: 700 }}>
                            Product Information
                          </Typography>
                        </Box>

                        <Stack spacing={4}>
                          {/* Serial Number */}
                          <Zoom in timeout={600} style={{ transitionDelay: '100ms' }}>
                            <HolographicCard>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <Box
                                  sx={{
                                    p: 1.5,
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.2)} 100%)`,
                                    animation: `${energyPulse} 2s infinite`,
                                    mr: 2,
                                  }}
                                >
                                  <Fingerprint sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
                                </Box>
                                <Box>
                                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                                    Serial Number
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Unique blockchain identifier
                                  </Typography>
                                </Box>
                              </Box>
                              <NeonTextField
                                fullWidth
                                label="Serial Number *"
                                name="serialNumber"
                                value={formData.serialNumber}
                                onChange={handleInputChange}
                                variant="outlined"
                                slotProps={{
                                  input: {
                                    startAdornment: (
                                      <Box sx={{ mr: 1, color: theme.palette.primary.main, fontWeight: 700 }}>
                                        #
                                      </Box>
                                    ),
                                  },
                                }}
                              />
                            </HolographicCard>
                          </Zoom>

                          {/* Product Name */}
                          <Zoom in timeout={600} style={{ transitionDelay: '200ms' }}>
                            <HolographicCard>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <Box
                                  sx={{
                                    p: 1.5,
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.main, 0.2)} 100%)`,
                                    animation: `${float} 3s ease-in-out infinite`,
                                    mr: 2,
                                  }}
                                >
                                  <Verified sx={{ color: theme.palette.success.main, fontSize: 28 }} />
                                </Box>
                                <Box>
                                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                                    Product Name
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Official product designation
                                  </Typography>
                                </Box>
                              </Box>
                              <NeonTextField
                                fullWidth
                                label="Product Name *"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                variant="outlined"
                              />
                            </HolographicCard>
                          </Zoom>

                          {/* Brand */}
                          <Zoom in timeout={600} style={{ transitionDelay: '300ms' }}>
                            <HolographicCard>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <Box
                                  sx={{
                                    p: 1.5,
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.main, 0.2)} 100%)`,
                                    animation: `${pulse} 2.5s ease-in-out infinite`,
                                    mr: 2,
                                  }}
                                >
                                  <Store sx={{ color: theme.palette.warning.main, fontSize: 28 }} />
                                </Box>
                                <Box>
                                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                                    Brand
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Manufacturer brand identity
                                  </Typography>
                                </Box>
                              </Box>
                              <NeonTextField
                                fullWidth
                                label="Brand *"
                                name="brand"
                                value={formData.brand}
                                onChange={handleInputChange}
                                variant="outlined"
                              />
                            </HolographicCard>
                          </Zoom>

                          {/* Description */}
                          <Zoom in timeout={600} style={{ transitionDelay: '400ms' }}>
                            <HolographicCard>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <Box
                                  sx={{
                                    p: 1.5,
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.2)} 100%)`,
                                    animation: `${shimmer} 3s ease-in-out infinite`,
                                    mr: 2,
                                  }}
                                >
                                  <Description sx={{ color: theme.palette.info.main, fontSize: 28 }} />
                                </Box>
                                <Box>
                                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                                    Description
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Detailed product specifications
                                  </Typography>
                                </Box>
                              </Box>
                              <NeonTextField
                                fullWidth
                                label="Description *"
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                variant="outlined"
                                multiline
                                rows={4}
                              />
                            </HolographicCard>
                          </Zoom>
                        </Stack>
                      </CardContent>
                    </GlassCard>
                  </Grid>

                  {/* Product Image Section */}
                  <Grid item xs={12} lg={4}>
                    <GlassCard sx={{ height: '100%' }}>
                      <CardContent sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                          <Image sx={{ color: theme.palette.secondary.main, mr: 2 }} />
                          <Typography variant="h6" sx={{ fontWeight: 700 }}>
                            Product Image
                          </Typography>
                        </Box>

                        <UploadZone component="label" sx={{ mb: 3, cursor: 'pointer' }}>
                          <input type="file" hidden accept="image/*" onChange={handleImageUpload} />
                          <CloudUpload sx={{ fontSize: 48, color: theme.palette.primary.main, mb: 2 }} />
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                            {image.filepreview ? 'Change Image' : 'Upload Product Image *'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Drag & drop or click to select
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                            JPEG, PNG • Max 5MB
                          </Typography>
                        </UploadZone>

                        {image.filepreview && (
                          <Zoom in timeout={500}>
                            <Card elevation={0} sx={{ borderRadius: '16px', overflow: 'hidden', mb: 3 }}>
                              <CardMedia
                                component="img"
                                height="200"
                                image={image.filepreview}
                                alt="Product preview"
                                sx={{ objectFit: 'contain' }}
                              />
                            </Card>
                          </Zoom>
                        )}

                        {/* Enhanced Manufacturer Info */}
                        <Zoom in timeout={800} style={{ transitionDelay: '600ms' }}>
                          <HolographicCard sx={{ p: 3, textAlign: 'left' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Box
                                sx={{
                                  p: 1,
                                  borderRadius: '8px',
                                  background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`,
                                  mr: 2,
                                }}
                              >
                                <Security sx={{ color: theme.palette.secondary.main, fontSize: 20 }} />
                              </Box>
                              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                Blockchain Identity
                              </Typography>
                            </Box>
                            <Stack spacing={2}>
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                p: 2,
                                borderRadius: '12px',
                                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.05)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
                                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                              }}>
                                <Person sx={{ color: theme.palette.info.main, fontSize: 20 }} />
                                <Box sx={{ flex: 1 }}>
                                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                                    MANUFACTURER
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontWeight: 700 }}>
                                    {manufacturerData.name || (
                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <CircularProgress size={16} />
                                        <span>Authenticating...</span>
                                      </Box>
                                    )}
                                  </Typography>
                                </Box>
                              </Box>
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                p: 2,
                                borderRadius: '12px',
                                background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.05)} 0%, ${alpha(theme.palette.success.main, 0.1)} 100%)`,
                                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                              }}>
                                <LocationOn sx={{ color: theme.palette.success.main, fontSize: 20 }} />
                                <Box sx={{ flex: 1 }}>
                                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                                    GEOLOCATION
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontWeight: 700 }}>
                                    {manufacturerData.latitude
                                      ? `${Number(manufacturerData.latitude).toFixed(4)}, ${Number(manufacturerData.longitude).toFixed(4)}`
                                      : (
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                          <CircularProgress size={16} />
                                          <span>Detecting...</span>
                                        </Box>
                                      )}
                                  </Typography>
                                </Box>
                              </Box>
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                p: 2,
                                borderRadius: '12px',
                                background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.05)} 0%, ${alpha(theme.palette.warning.main, 0.1)} 100%)`,
                                border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                              }}>
                                <Schedule sx={{ color: theme.palette.warning.main, fontSize: 20 }} />
                                <Box sx={{ flex: 1 }}>
                                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                                    TIMESTAMP
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontWeight: 700 }}>
                                    {manufacturerData.date ? new Date(manufacturerData.date * 1000).toLocaleString() : 'Generating...'}
                                  </Typography>
                                </Box>
                              </Box>
                            </Stack>
                          </HolographicCard>
                        </Zoom>
                      </CardContent>
                    </GlassCard>
                  </Grid>
                </Grid>
              ) : (
                /* Enhanced QR Code Success Section */
                <Zoom in timeout={1000}>
                  <GlassCard sx={{ textAlign: 'center', position: 'relative', overflow: 'visible' }}>
                    {/* Success Celebration Effect */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -20,
                        left: -20,
                        right: -20,
                        bottom: -20,
                        background: `radial-gradient(circle, ${alpha(theme.palette.success.main, 0.1)} 0%, transparent 70%)`,
                        animation: `${energyPulse} 2s infinite`,
                        borderRadius: '50px',
                        pointerEvents: 'none',
                      }}
                    />

                    <CardContent sx={{ p: 6, position: 'relative' }}>
                      {/* Header with Holographic Effect */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 4 }}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: '20px',
                            background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.2)} 0%, ${alpha(theme.palette.success.main, 0.3)} 100%)`,
                            animation: `${glow} 3s ease-in-out infinite`,
                            mr: 3,
                          }}
                        >
                          <QrCode sx={{ color: theme.palette.success.main, fontSize: 40 }} />
                        </Box>
                        <Box sx={{ textAlign: 'left' }}>
                          <Typography
                            variant="h3"
                            sx={{
                              fontWeight: 800,
                              background: `linear-gradient(135deg,
                                ${theme.palette.success.main} 0%,
                                ${theme.palette.success.light} 50%,
                                ${theme.palette.success.dark} 100%
                              )`,
                              backgroundClip: 'text',
                              WebkitBackgroundClip: 'text',
                              WebkitTextFillColor: 'transparent',
                              mb: 1,
                            }}
                          >
                            Registration Complete
                          </Typography>
                          <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 600 }}>
                            Blockchain Verification Successful
                          </Typography>
                        </Box>
                      </Box>

                      {/* Success Message */}
                      <Box sx={{ mb: 4 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                          🎉 Product Successfully Registered on Blockchain!
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          Your product is now permanently secured and verifiable through our decentralized network
                        </Typography>
                      </Box>

                      {/* Enhanced QR Code Display */}
                      <Box sx={{ mb: 4 }}>
                        <HolographicCard
                          sx={{
                            display: 'inline-block',
                            p: 4,
                            position: 'relative',
                            '&::before': {
                              animation: `${hologram} 4s ease-in-out infinite`,
                            }
                          }}
                        >
                          <Box
                            sx={{
                              position: 'relative',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: -10,
                                left: -10,
                                right: -10,
                                bottom: -10,
                                background: `conic-gradient(from 0deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, ${theme.palette.success.main}, ${theme.palette.primary.main})`,
                                borderRadius: '20px',
                                animation: `${float} 4s ease-in-out infinite`,
                                opacity: 0.3,
                                zIndex: -1,
                              }
                            }}
                          >
                            <QRCode id="QRCode" value={qrData} size={220} />
                          </Box>
                        </HolographicCard>
                      </Box>

                      {/* Enhanced Action Buttons */}
                      <Stack direction="row" spacing={3} justifyContent="center">
                        <CyberButton
                          variant="primary"
                          onClick={downloadQR}
                          startIcon={<Download />}
                          sx={{
                            minWidth: 200,
                            py: 2,
                            fontSize: '1.1rem',
                            animation: `${shimmer} 3s ease-in-out infinite`,
                          }}
                        >
                          Download QR Code
                        </CyberButton>
                        <CyberButton
                          variant="secondary"
                          onClick={() => setRegistrationComplete(false)}
                          startIcon={<Add />}
                          sx={{
                            minWidth: 200,
                            py: 2,
                            fontSize: '1.1rem',
                          }}
                        >
                          Register Another
                        </CyberButton>
                      </Stack>
                    </CardContent>
                  </GlassCard>
                </Zoom>
              )}

              {/* Loading Status */}
              {loading && (
                <Zoom in timeout={500}>
                  <GlassCard sx={{ textAlign: 'center', mb: 4 }}>
                    <CardContent sx={{ p: 4 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                        <Security sx={{ color: theme.palette.primary.main, mr: 2 }} />
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          Processing Registration
                        </Typography>
                      </Box>
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                        Registering product on blockchain...
                      </Typography>
                      <LinearProgress
                        sx={{
                          borderRadius: 2,
                          height: 8,
                          background: alpha(theme.palette.primary.main, 0.1),
                          '& .MuiLinearProgress-bar': {
                            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                            borderRadius: 2,
                          }
                        }}
                      />
                    </CardContent>
                  </GlassCard>
                </Zoom>
              )}

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', mt: 4 }}>
                <CyberButton
                  variant="secondary"
                  onClick={() => navigate(-1)}
                  disabled={loading}
                  startIcon={<ArrowBack />}
                  sx={{
                    minWidth: 160,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    textTransform: 'none',
                  }}
                >
                  {registrationComplete ? 'Back to Dashboard' : 'Cancel'}
                </CyberButton>

                <CyberButton
                  variant="primary"
                  type="submit"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Add />}
                  sx={{
                    minWidth: 220,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    textTransform: 'none',
                  }}
                >
                  {loading
                    ? 'Processing...'
                    : registrationComplete
                      ? 'Register New Product'
                      : 'Register Product'}
                </CyberButton>
              </Box>

              {/* Footer */}
              <Divider sx={{ my: 4 }} />
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Secure Blockchain Product Registration • Powered by Verifai
                </Typography>
              </Box>
            </form>

            {/* Snackbar for notifications */}
            <Snackbar
              open={transactionStatus.open}
              autoHideDuration={6000}
              onClose={handleCloseSnackbar}
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <Alert
                onClose={handleCloseSnackbar}
                severity={transactionStatus.severity}
                sx={{
                  width: '100%',
                  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                  borderRadius: '12px',
                }}
              >
                {transactionStatus.message}
              </Alert>
            </Snackbar>
          </Box>
        </Fade>
      </Container>
    </FuturisticContainer>
  );
};

export default AddProduct;