import {
    Box,
    Paper,
    Avatar,
    Typo<PERSON>,
    Button,
    Container,
    Card,
    CardContent,
    Chip,
    Stack,
    useTheme,
    alpha,
    IconButton,
    Tooltip,
    Fade,
    Zoom,
    Grid
} from '@mui/material';
import {
    CheckCircle,
    Security,
    Fingerprint,
    ArrowBack,
    Schedule,
    LocationOn,
    Person,
    CalendarToday,
    Verified,
    Timeline as TimelineIcon,
    ShoppingCart,
    Inventory
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';
import Timeline from '@mui/lab/Timeline';
import TimelineItem from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineOppositeContent, {
    timelineOppositeContentClasses,
} from '@mui/lab/TimelineOppositeContent';
import dayjs from 'dayjs';
import { useLocation, useNavigate } from 'react-router-dom';
import abi from '../../utils/VerifaiDebug.json';
import { useEffect, useState } from 'react';
import { BrowserProvider, Contract } from "ethers";

// Professional animations
const smoothSlideIn = keyframes`
  0% {
    transform: translateY(40px);
    opacity: 0;
    filter: blur(10px);
  }
  100% {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0);
  }
`;

const gentleFloat = keyframes`
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-4px) rotate(1deg);
  }
  50% {
    transform: translateY(-8px) rotate(0deg);
  }
  75% {
    transform: translateY(-4px) rotate(-1deg);
  }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
`;

// Professional styled components
const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: theme.palette.mode === 'dark' ? '#121212' : '#ffffff',
  position: 'relative',
  overflow: 'hidden',
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '24px',
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  animation: `${gentleFloat} 6s ease-in-out infinite`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `
      0 20px 40px ${alpha(theme.palette.common.black, 0.15)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.3)}
    `,
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  borderRadius: '12px',
  fontWeight: 600,
  padding: '8px 4px',
  background: status === 'success'
    ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
    : status === 'error'
    ? `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`
    : `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
  color: theme.palette.common.white,
  boxShadow: `0 4px 15px ${alpha(
    status === 'success' ? theme.palette.success.main :
    status === 'error' ? theme.palette.error.main : theme.palette.warning.main,
    0.3
  )}`,
  animation: status === 'processing' ? `${pulse} 2s ease-in-out infinite` : 'none',
}));

const InfoCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(15px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '20px',
  padding: '24px',
  margin: '16px 0',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const CyberButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '16px',
  padding: '12px 32px',
  fontWeight: 700,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: variant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: variant === 'secondary' ? `2px solid ${alpha(theme.palette.primary.main, 0.3)}` : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
  },
}));

const getEthereumObject = () => window.ethereum;

/*
 * This function returns the first linked account found.
 * If there is no account linked, it will return null.
 */
const findMetaMaskAccount = async () => {
    try {
        const ethereum = getEthereumObject();

        /*
        * First make sure we have access to the Ethereum object.
        */
        if (!ethereum) {
            console.error("Make sure you have Metamask!");
            return null;
        }

        console.log("We have the Ethereum object", ethereum);
        const accounts = await ethereum.request({ method: "eth_accounts" });

        if (accounts.length !== 0) {
            const account = accounts[0];
            console.log("Found an authorized account:", account);
            return account;
        } else {
            console.error("No authorized account found");
            return null;
        }
    } catch (error) {
        console.error(error);
        return null;
    }
};



const Product = () => {
    const [currentAccount, setCurrentAccount] = useState("");

    const [serialNumber, setSerialNumber] = useState("");
    const [productData, setProductData] = useState("");

    const [name, setName] = useState("Loading...");
    const [brand, setBrand] = useState("Fetching data...");
    const [description, setDescription] = useState("Please wait while we retrieve product information from the blockchain...");
    const [history, setHistory] = useState([]);
    const [isSold, setIsSold] = useState(false);
    const [toUpdate, setToUpdate] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [image, setImage] = useState({
        file: [],
        filepreview: null
    });



    const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "******************************************";
    const CONTRACT_ABI = abi.abi;

    const navigate = useNavigate();
    const location = useLocation();
    const qrData = location.state?.qrData || new URLSearchParams(location.search).get('qr');
    const refreshKey = location.state?.refreshKey;

    useEffect(() => {
        console.log("useEffect 1");

        // Parallel execution for better performance
        const initializeData = async () => {
            // Start both operations simultaneously
            const accountPromise = findMetaMaskAccount();
            const scanPromise = qrData ? handleScan(qrData) : Promise.resolve();

            try {
                const [account] = await Promise.all([accountPromise, scanPromise]);
                if (account !== null) {
                    setCurrentAccount(account);
                }
            } catch (error) {
                console.error("Error initializing data:", error);
            }
        };

        initializeData();
    }, [qrData, refreshKey]);


    const getImage = async (imageName) => {
        setImage(prevState => ({
            ...prevState,
            filepreview: `${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/file/product/${imageName}`
            })
        )
    }

    const handleScan = async (qrData) => {
        console.log("🔍 handleScan called with QR data:", qrData);

        setIsLoading(true);

        const data = qrData.split(",");
        const contractAddress = data[0];
        const serialNumber = data[1];

        setSerialNumber(serialNumber);

        console.log("📋 Contract address:", contractAddress);
        console.log("📋 Serial number:", serialNumber);
        console.log("📋 Expected contract address:", CONTRACT_ADDRESS);

        if (contractAddress === CONTRACT_ADDRESS) {
            console.log("✅ Contract address matches, proceeding with blockchain call...");
            try {
                const { ethereum } = window;

                if (!ethereum) {
                    console.error("❌ MetaMask not found!");
                    setName("MetaMask Required");
                    setBrand("Please install MetaMask");
                    setDescription("MetaMask wallet is required to view product details");
                    setIsLoading(false);
                    return;
                }

                console.log("🔗 MetaMask detected, connecting...");

                // Enhanced blockchain interaction with better error handling
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Blockchain request timeout after 15 seconds')), 15000)
                );

                const blockchainPromise = (async () => {
                    try {
                        // Request account access if needed
                        console.log("🔐 Requesting account access...");
                        await ethereum.request({ method: 'eth_requestAccounts' });

                        console.log("🌐 Creating provider and signer...");
                        const provider = new BrowserProvider(ethereum);
                        const signer = await provider.getSigner();

                        console.log("📄 Creating contract instance...");
                        const productContract = new Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

                        console.log("🔍 Fetching product data for serial:", serialNumber);
                        const product = await productContract.getProduct(serialNumber.toString());

                        console.log("✅ Raw blockchain response:", product);
                        return product;
                    } catch (innerError) {
                        console.error("❌ Inner blockchain error:", innerError);
                        throw innerError;
                    }
                })();

                // Race between blockchain call and timeout
                const product = await Promise.race([blockchainPromise, timeoutPromise]);

                console.log("✅ Retrieved product from blockchain:", product);
                console.log("✅ Product type:", typeof product);
                console.log("✅ Product length:", product?.length);

                // Enhanced validation
                if (!product) {
                    console.error("❌ Product is null or undefined");
                    setIsLoading(false);
                    navigate('/fake-product');
                    return;
                }

                if (Array.isArray(product) && product.length === 0) {
                    console.error("❌ Product array is empty");
                    setIsLoading(false);
                    navigate('/fake-product');
                    return;
                }

                if (Array.isArray(product) && (!product[0] || product[0] === "")) {
                    console.error("❌ Product serial number is empty");
                    setIsLoading(false);
                    navigate('/fake-product');
                    return;
                }

                console.log("✅ Product validation passed, processing data...");
                console.log("✅ Product structure:", {
                    serialNumber: product[0],
                    name: product[1],
                    brand: product[2],
                    description: product[3],
                    image: product[4],
                    history: product[5],
                    historyType: typeof product[5],
                    historyLength: product[5]?.length
                });

                // Parse the structured data
                setStructuredData(product);
                setIsLoading(false);

            } catch (error) {
                console.error("❌ Blockchain error:", error);
                setIsLoading(false);

                // Provide user-friendly error handling
                if (error.message.includes('timeout')) {
                    setName("Connection Timeout");
                    setBrand("Blockchain Unavailable");
                    setDescription("Unable to connect to blockchain. Please check your internet connection and try again.");
                } else if (error.message.includes('rejected')) {
                    setName("Transaction Rejected");
                    setBrand("User Cancelled");
                    setDescription("MetaMask transaction was rejected. Please try again.");
                } else if (error.message.includes('network')) {
                    setName("Network Error");
                    setBrand("Wrong Network");
                    setDescription("Please ensure you're connected to the correct blockchain network.");
                } else {
                    setName("Error Loading Product");
                    setBrand("Blockchain Error");
                    setDescription(`Unable to load product data: ${error.message}`);
                }
            }
        } else {
            console.error("❌ Contract address mismatch!");
            console.error("❌ Expected:", CONTRACT_ADDRESS);
            console.error("❌ Received:", contractAddress);
            setName("Invalid QR Code");
            setBrand("Wrong Contract");
            setDescription("This QR code is not from the correct Verifai system.");
            setIsLoading(false);
        }
    };

    // New function to handle structured data from smart contract
    const setStructuredData = (product) => {
        console.log("Setting structured product data:", product);

        // Extract basic product info
        setSerialNumber(product[0]);
        setName(product[1]);
        setBrand(product[2]);
        setDescription(product[3].replace(/;/g, ","));
        getImage(product[4]);

        // Extract and process history array
        const historyArray = product[5]; // This is the ProductHistory[] array
        console.log("Raw history array:", historyArray);

        const hist = [];
        for (let i = 0; i < historyArray.length; i++) {
            const historyEntry = historyArray[i];
            console.log("Processing history entry:", historyEntry);

            const actor = historyEntry.actor;
            const location = historyEntry.location.replace(/;/g, ",");
            const timestamp = historyEntry.timestamp;
            const isSold = historyEntry.isSold; // This is already a boolean from the smart contract

            console.log("History entry details:", {
                actor,
                location,
                timestamp,
                isSold,
                isSoldType: typeof isSold
            });

            hist.push({
                actor, location, timestamp, isSold
            });
        }

        console.log("Processed history:", hist);
        console.log("History length:", hist.length);
        console.log("History entries details:", hist.map((h, i) => `${i}: ${h.actor} at ${h.location} on ${h.timestamp}`));
        setHistory(hist);

        // Set the overall product's isSold status based on the latest history entry
        if (hist.length > 0) {
            const latestEntry = hist[hist.length - 1];
            setIsSold(latestEntry.isSold);
            console.log("Latest history entry:", latestEntry);
            console.log("Product isSold status:", latestEntry.isSold);
            console.log("Product isSold type:", typeof latestEntry.isSold);
        }
    }

    const setData = (d) => {
        console.log("product data: ", d);

        const arr = d.split(",");
        console.log("arr", arr)

        setName(arr[1]);
        setBrand(arr[2]);
        setDescription(arr[3].replace(/;/g, ","));
        // setImage(arr[4]);
        getImage(arr[4]);

        const hist = [];
        let start = 5;

        for (let i = 5; i < arr.length; i += 5) {
            const actor = arr[start + 1];
            const location = arr[start + 2].replace(/;/g, ",");
            const timestamp = arr[start + 3];
            const isSold = arr[start + 4] === "true";

            hist.push({
                actor, location, timestamp, isSold
            });

            start += 5;
        }
        console.log("hist", hist)
        setHistory(hist);

        // Set the overall product's isSold status based on the latest history entry
        if (hist.length > 0) {
            const latestEntry = hist[hist.length - 1];
            setIsSold(latestEntry.isSold);
            console.log("Product isSold status:", latestEntry.isSold);
        }

    }

    const handleBack = () => {
        navigate(-2)
    }


    const getHistory = () => {
        console.log("🔍 getHistory called with history array:", history);
        console.log("🔍 History array length:", history.length);

        if (history.length === 0) {
            console.log("⚠️ History array is empty - no supply chain updates found");
            return [];
        }

        return history.map((item, index) => {
            const date = dayjs(item.timestamp * 1000).format('MM/DD/YYYY');
            const time = dayjs(item.timestamp * 1000).format('HH:mm a');
            console.log(`📋 Rendering history entry ${index}:`, {
                actor: item.actor,
                location: item.location,
                timestamp: item.timestamp,
                date,
                time
            });

            return (
                <TimelineItem key={index}>
                    <TimelineOppositeContent color="textSecondary">
                        {time} {date}
                    </TimelineOppositeContent>
                    <TimelineSeparator>
                        <TimelineDot />
                        <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent sx={{ py: '12px', px: 2 }}>
                        <Typography>Location: {item.location}</Typography>
                        <Typography>Actor: {item.actor}</Typography>
                    </TimelineContent>
                </TimelineItem>
            );
        });
    }


    const theme = useTheme();

    return (
        <FuturisticContainer>
            <Container maxWidth="lg" sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '100vh',
                py: 6,
                position: 'relative',
                zIndex: 10
            }}>
                <GlassCard sx={{
                    maxWidth: 900,
                    width: '100%',
                    p: 6,
                    animation: `${smoothSlideIn} 1s cubic-bezier(0.4, 0, 0.2, 1)`,
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '4px',
                        background: `linear-gradient(90deg,
                            ${theme.palette.success.main} 0%,
                            ${theme.palette.primary.main} 50%,
                            ${theme.palette.success.dark} 100%
                        )`,
                        borderRadius: '24px 24px 0 0',
                    },
                }}>
                    {/* Success Header */}
                    <Fade in timeout={800}>
                        <Box sx={{ textAlign: 'center', mb: 6 }}>
                            <Box
                                sx={{
                                    width: 120,
                                    height: 120,
                                    mx: 'auto',
                                    mb: 4,
                                    borderRadius: '50%',
                                    background: `linear-gradient(135deg,
                                        ${theme.palette.success.main} 0%,
                                        ${theme.palette.success.light} 25%,
                                        ${theme.palette.primary.main} 50%,
                                        ${theme.palette.success.dark} 75%,
                                        ${theme.palette.success.main} 100%
                                    )`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    animation: `${gentleFloat} 6s ease-in-out infinite`,
                                    boxShadow: `
                                        0 20px 60px ${alpha(theme.palette.success.main, 0.3)},
                                        0 0 0 4px ${alpha(theme.palette.success.main, 0.1)},
                                        inset 0 2px 0 ${alpha(theme.palette.common.white, 0.3)}
                                    `,
                                }}
                            >
                                <Verified sx={{ fontSize: 48, color: 'white' }} />
                            </Box>

                            <StatusChip
                                status="success"
                                icon={<CheckCircle />}
                                label="Product Authenticated"
                                sx={{ mb: 3, fontSize: '1.1rem', px: 3, py: 1 }}
                            />

                            <Typography
                                variant="h3"
                                component="h1"
                                sx={{
                                    mb: 2,
                                    fontSize: { xs: '2rem', md: '2.5rem' },
                                    fontWeight: 800,
                                    background: `linear-gradient(135deg,
                                        ${theme.palette.success.main} 0%,
                                        ${theme.palette.primary.main} 50%,
                                        ${theme.palette.success.dark} 100%
                                    )`,
                                    backgroundClip: 'text',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    letterSpacing: '-0.02em',
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                }}
                            >
                                Product Details
                            </Typography>

                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    mb: 4,
                                    maxWidth: 600,
                                    mx: 'auto',
                                    fontWeight: 500,
                                    fontSize: '1.1rem',
                                    lineHeight: 1.6,
                                }}
                            >
                                Blockchain verification successful. All product information has been validated and authenticated.
                            </Typography>
                        </Box>
                    </Fade>

                    {/* Product Information Section */}
                    <Zoom in timeout={1000}>
                        <Grid container spacing={4} sx={{ mb: 6 }}>
                            {/* Product Image and Basic Info */}
                            <Grid item xs={12} md={6}>
                                <InfoCard>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                        <Inventory sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 28 }} />
                                        <Typography variant="h5" sx={{ fontWeight: 700 }}>
                                            Product Information
                                        </Typography>
                                    </Box>

                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                                        <Avatar
                                            alt={name}
                                            src={image.filepreview}
                                            sx={{
                                                width: 120,
                                                height: 120,
                                                mr: 3,
                                                border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                                boxShadow: `0 8px 24px ${alpha(theme.palette.common.black, 0.15)}`,
                                            }}
                                        >
                                            {name?.charAt(0)}
                                        </Avatar>

                                        <Box sx={{ flex: 1 }}>
                                            <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                                                {name}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                Brand: {brand}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {description}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </InfoCard>
                            </Grid>

                            {/* Product Details */}
                            <Grid item xs={12} md={6}>
                                <InfoCard>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                        <Fingerprint sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 28 }} />
                                        <Typography variant="h5" sx={{ fontWeight: 700 }}>
                                            Authentication Details
                                        </Typography>
                                    </Box>

                                    <Stack spacing={3}>
                                        <Box>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                Serial Number
                                            </Typography>
                                            <Typography variant="h6" sx={{
                                                fontFamily: '"JetBrains Mono", "Fira Code", monospace',
                                                color: theme.palette.primary.main,
                                                fontWeight: 600
                                            }}>
                                                #{serialNumber}
                                            </Typography>
                                        </Box>

                                        <Box>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                Sale Status
                                            </Typography>
                                            <StatusChip
                                                status={isSold ? "warning" : "success"}
                                                icon={isSold ? <ShoppingCart /> : <Inventory />}
                                                label={isSold ? "Sold" : "Available"}
                                                size="small"
                                            />
                                        </Box>

                                        <Box>
                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                Verification Time
                                            </Typography>
                                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                                {dayjs().format('MMMM DD, YYYY [at] HH:mm A')}
                                            </Typography>
                                        </Box>
                                    </Stack>
                                </InfoCard>
                            </Grid>
                        </Grid>
                    </Zoom>

                    {/* Product History Timeline */}
                    <Fade in timeout={1200}>
                        <InfoCard sx={{ mb: 4 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                                <TimelineIcon sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 28 }} />
                                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                                    Product History
                                </Typography>
                            </Box>

                            <Timeline
                                sx={{
                                    [`& .${timelineOppositeContentClasses.root}`]: {
                                        flex: 0.3,
                                    },
                                    '& .MuiTimelineItem-root': {
                                        '&:before': {
                                            display: 'none',
                                        },
                                    },
                                    '& .MuiTimelineDot-root': {
                                        backgroundColor: theme.palette.primary.main,
                                        boxShadow: `0 0 0 4px ${alpha(theme.palette.primary.main, 0.2)}`,
                                    },
                                    '& .MuiTimelineConnector-root': {
                                        backgroundColor: alpha(theme.palette.primary.main, 0.3),
                                    },
                                }}
                            >
                                {getHistory()}
                                <TimelineItem>
                                    <TimelineOppositeContent color="text.secondary" sx={{ fontWeight: 600 }}>
                                        {dayjs().format('HH:mm A')}
                                        <br />
                                        {dayjs().format('MMM DD, YYYY')}
                                    </TimelineOppositeContent>
                                    <TimelineSeparator>
                                        <TimelineDot sx={{
                                            backgroundColor: theme.palette.success.main,
                                            boxShadow: `0 0 0 4px ${alpha(theme.palette.success.main, 0.2)}`,
                                        }} />
                                    </TimelineSeparator>
                                    <TimelineContent sx={{ py: '12px', px: 2 }}>
                                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                            Product Verified
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Blockchain authentication completed successfully
                                        </Typography>
                                    </TimelineContent>
                                </TimelineItem>
                            </Timeline>
                        </InfoCard>
                    </Fade>

                    {/* Action Buttons */}
                    <Fade in timeout={1400}>
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                            <CyberButton
                                variant="secondary"
                                onClick={handleBack}
                                startIcon={<ArrowBack />}
                                sx={{ px: 4, py: 1.5 }}
                            >
                                Back to Scanner
                            </CyberButton>
                        </Box>
                    </Fade>
                </GlassCard>
            </Container>
        </FuturisticContainer>
    )
}

export default Product;