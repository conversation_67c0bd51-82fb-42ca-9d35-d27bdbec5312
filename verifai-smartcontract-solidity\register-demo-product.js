const hre = require("hardhat");

async function main() {
    console.log("🚀 Registering demo product for supervisor presentation...");

    // Get signers from Hardhat
    const [deployer] = await hre.ethers.getSigners();

    console.log("👤 Using account:", deployer.address);

    const balance = await deployer.getBalance();
    console.log("💰 Account balance:", hre.ethers.utils.formatEther(balance), "ETH");

    // Contract details
    const contractAddress = "******************************************";

    // Get contract factory and attach to deployed contract
    const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
    const contract = VerifaiFactory.attach(contractAddress);
    
    console.log("📋 Contract address:", contractAddress);
    
    // Register a test product
    const productData = {
        name: "Demo Product",
        brand: "Verifai Demo",
        serialNumber: "DEMO123",
        description: "Test product for supervisor demonstration",
        image: "demo-product.jpg",
        actor: "Demo Manufacturer",
        location: "Demo Location, Demo City",
        timestamp: Math.floor(Date.now() / 1000).toString()
    };
    
    console.log("📦 Registering product:", productData.serialNumber);
    
    try {
        const tx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction hash:", tx.hash);
        await tx.wait();
        console.log("✅ Product registered successfully!");
        
        // Verify the product
        console.log("🔍 Verifying product...");
        const product = await contract.getProduct(productData.serialNumber);
        console.log("✅ Product verified!");
        console.log("📦 Product name:", product[1]);
        console.log("🏷️  Product brand:", product[2]);
        
        console.log("\n🎯 QR Code for testing:");
        console.log(`${contractAddress},${productData.serialNumber}`);
        
        console.log("\n✅ SUCCESS! Your demo product is ready!");
        console.log("🔗 You can now scan this QR code in your frontend");
        
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
